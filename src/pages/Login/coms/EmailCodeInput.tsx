import React from 'react';
import { Form, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types/login';
import FormActions from './FormActions';
import SendCodeInput from '@/components/SendCodeInput';

interface EmailCodeInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'emailCode'>) => void;
  onSwitchToPassword: () => void;
  userEmail?: string; // 添加用户邮箱显示
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  form,
  onSubmit,
  onSwitchToPassword,
  userEmail,
}) => {
  const { t } = useTranslation();
  const handleSubmit = () => {
    if (form && onSubmit) {
      form
        .validateFields(['emailCode'])
        .then(values => {
          onSubmit(values);
        })
        .catch(errorInfo => {
          console.log('Email code validation failed:', errorInfo);
        });
    }
  };

  // 重新发送验证码
  const handleResendCode = async () => {
    try {
      console.log('重新发送验证码到邮箱:', userEmail);
      // 这里可以调用发送验证码API，使用userEmail
      // 例如：await api.sendEmailCode({ email: userEmail });

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('验证码发送成功');
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error; // 重新抛出错误，让组件处理
    }
  };

  return (
    <>
      <Form.Item
        label={
          <span className="leading-10">
            {t('auth.login.form.emailCodeLabel')}
          </span>
        }
        name="emailCode"
        rules={[{ required: true, message: t('common.form.required') }]}
        validateTrigger={['onChange', 'onBlur']}
        className="mb-6"
      >
        <SendCodeInput onSendCode={handleResendCode} />
      </Form.Item>

      <FormActions
        buttonText={t('auth.login.form.submit')}
        onSubmit={handleSubmit}
      />
      <div className="mt-6 text-center">
        <span
          className="cursor-pointer text-[12px] text-[#ff5e13] hover:underline"
          onClick={onSwitchToPassword}
        >
          {t('auth.login.form.loginWithPassword')}
        </span>
      </div>
    </>
  );
};

export default EmailCodeInput;

import React, { useState, useEffect } from 'react';
import { Form, message, ConfigProvider } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

import UserNameInput from './coms/UserNameInput';
import EmailCodeInput from './coms/EmailCodeInput';
import PasswordInput from './coms/PasswordInput';
import type { LoginFormData } from '@/types/login';
import SelectLanguage from '@/components/SelectLanguage';
import { useAuthStore } from '@/store/authStore';
import { useNavigate, useLocation } from 'react-router-dom';

import logoIcon from '@/assets/logo.svg';

type LoginStep = 'userName' | 'auth';
type AuthMethod = 'email' | 'password';

const Login: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const [currentStep, setCurrentStep] = useState<LoginStep>('userName');
  const [authMethod, setAuthMethod] = useState<AuthMethod>('password');
  const [form] = Form.useForm<LoginFormData>();
  useEffect(() => {
    form.setFieldsValue({
      email: '<EMAIL>',
      password: 'password123',
    });
  }, []);

  // 用户名提交
  const handleUsernameSubmit = (values: { email: string }) => {
    form.setFieldsValue({ email: values.email });

    setCurrentStep('auth');
  };

  // 密码登录提交
  const handlePasswordSubmit = (values: Pick<LoginFormData, 'password'>) => {
    form.setFieldsValue({ password: values.password });

    handleLogin();
  };

  // 邮箱验证码登录提交
  const handleEmailCodeSubmit = (values: Pick<LoginFormData, 'emailCode'>) => {
    form.setFieldsValue({ emailCode: values.emailCode });

    handleLogin();
  };

  const { login } = useAuthStore();
  // 统一的登录处理函数
  const handleLogin = async () => {
    const loginData = form.getFieldsValue(true);
    console.log('loginData----', loginData);

    try {
      // 调用实际的登录API
      await login(loginData);
      // TODO 跳转的逻辑在RedirectIfAuthenticated中处理
      // const url = location.state?.from?.pathname || '/';
      // navigate(url, { replace: true });
      // console.log('登录成功跳转到', url);

      message.success('登录成功');
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试');
    }
  };

  // 切换到邮箱验证码登录
  const handleSwitchToEmailCode = () => {
    setAuthMethod('email');
    form.setFieldsValue({ password: '', emailCode: '' });

    console.log('清空密码和邮箱验证码');
  };

  // 切换到密码登录
  const handleSwitchToPassword = () => {
    setAuthMethod('password');
    form.setFieldsValue({ password: '', emailCode: '' });
    console.log('清空密码和邮箱验证码');
  };
  const getEmail = () => {
    const { email } = form.getFieldsValue(true);
    return email;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'userName':
        return <UserNameInput form={form} onSubmit={handleUsernameSubmit} />;
      case 'auth':
        return authMethod === 'email' ? (
          <EmailCodeInput
            form={form}
            onSubmit={handleEmailCodeSubmit}
            onSwitchToPassword={handleSwitchToPassword}
          />
        ) : (
          <PasswordInput
            form={form}
            onSubmit={handlePasswordSubmit}
            onSwitchToEmailCode={handleSwitchToEmailCode}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelFontSize: 12,
          },
        },
      }}
    >
      <div className="relative min-h-screen flex items-center justify-center bg-black text-[#656565]">
        <div className="absolute inset-0 bg-[#0d0d0d]" />

        <div className="relative z-10 h-520px flex flex-col items-center">
          <div className="mb-8">
            <img
              src={logoIcon}
              alt="Yuequ Logo"
              className="h-[60px] w-[69px]"
            />
          </div>

          <h1 className="font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold">
            {t('auth.login.title')}
          </h1>

          <div className="w-[496px]">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={(changedValues, allValues) => {
                // console.log('Form values changed:', changedValues, allValues);
              }}
            >
              {renderStepContent()}
            </Form>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Login;
